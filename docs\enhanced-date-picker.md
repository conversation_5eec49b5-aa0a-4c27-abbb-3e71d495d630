# Enhanced Date Picker Documentation

## Overview

The date picker component has been enhanced to provide a much better user experience, especially for selecting historical dates in transaction forms.

## New Features

### 1. Manual Date Input
- **Edit Button**: Click the pencil icon to enable manual date input
- **Multiple Formats**: Supports various date formats:
  - `dd/MM/yyyy` (15/03/2024)
  - `dd-MM-yyyy` (15-03-2024)
  - `dd.MM.yyyy` (15.03.2024)
  - `yyyy-MM-dd` (2024-03-15)
  - `dd/MM/yy` (15/03/24)
  - `d/M/yyyy` (5/3/2024) - single digit support
- **Real-time Validation**: Shows error messages for invalid dates
- **Keyboard Support**: 
  - Enter to confirm
  - Escape to cancel

### 2. Enhanced Calendar Navigation
- **Year Dropdown**: Quickly jump to any year (1970-2030)
- **Month Dropdown**: Quickly select any month
- **Romanian Localization**: Month names in Romanian
- **Historical Date Support**: Easy navigation to dates years in the past

### 3. Improved Validation
- **Format Validation**: Clear error messages for invalid formats
- **Range Validation**: Respects min/max date constraints
- **User-friendly Messages**: Error messages in Romanian
- **Visual Feedback**: Red borders and error text for invalid input

## Usage

### Basic Usage
```tsx
import { DatePicker } from "@/components/ui/date-picker-simple";

<DatePicker
  value={selectedDate}
  onChange={setSelectedDate}
  placeholder="Selectează data"
/>
```

### With Constraints
```tsx
<DatePicker
  value={transactionDate}
  onChange={setTransactionDate}
  placeholder="Selectează data tranzacției"
  maxDate={yesterday}
  minDate={oneYearAgo}
/>
```

### Disable Manual Input
```tsx
<DatePicker
  value={selectedDate}
  onChange={setSelectedDate}
  allowManualInput={false}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `Date \| undefined` | - | Selected date |
| `onChange` | `(date: Date \| undefined) => void` | - | Date change handler |
| `placeholder` | `string` | "Selectează data" | Input placeholder |
| `disabled` | `boolean` | `false` | Disable the component |
| `className` | `string` | - | Additional CSS classes |
| `maxDate` | `Date` | - | Maximum selectable date |
| `minDate` | `Date` | - | Minimum selectable date |
| `error` | `boolean` | `false` | Show error state |
| `allowManualInput` | `boolean` | `true` | Enable manual input feature |
| `id` | `string` | - | Input ID |

## Implementation Details

### Date Parsing
The component uses a robust date parsing system that:
- Tries multiple date formats in order of preference
- Validates year ranges (1900-2100)
- Handles single-digit days and months
- Provides fallback to native Date parsing

### Calendar Integration
- Uses `react-day-picker` with dropdown navigation
- Automatically sets year range for dropdowns
- Maintains Romanian localization
- Preserves all existing calendar features

### Validation System
- Real-time validation during typing
- Comprehensive error messages
- Respects min/max date constraints
- Visual feedback for invalid states

## Testing

A test page is available at `/test-date-picker` to verify all functionality:
- Manual input with various formats
- Calendar navigation
- Validation scenarios
- Edge cases

## Migration

The enhanced date picker is backward compatible. Existing usage will continue to work with the new features enabled by default. To disable manual input:

```tsx
<DatePicker allowManualInput={false} {...otherProps} />
```

## Benefits

1. **Faster Historical Date Selection**: No more clicking through months/years
2. **Flexible Input**: Users can type dates in their preferred format
3. **Better UX**: Clear validation and error messages
4. **Accessibility**: Keyboard navigation and screen reader support
5. **Consistency**: Maintains existing design and behavior
