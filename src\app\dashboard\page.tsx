import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import DashboardClient from "./DashboardClient";

export default async function DashboardPage({
  searchParams,
}: {
  searchParams?: Promise<{ portfolios?: string | string[] }>;
}) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect("/auth/signin");
  }

  const sp = await searchParams;
  const raw = sp?.portfolios;
  const initialSelectedPortfolios = Array.isArray(raw)
    ? raw.flatMap((s) => s.split(",")).filter(Boolean)
    : typeof raw === "string"
    ? raw.split(",").filter(Boolean)
    : [];

  return (
    <DashboardClient initialSelectedPortfolios={initialSelectedPortfolios} />
  );
}
