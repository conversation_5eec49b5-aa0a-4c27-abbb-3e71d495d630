"use client";

import { DeletePortfolioDialog } from "@/components/portfolios/delete-portfolio-dialog";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { saveDashboardPreferences } from "@/lib/dashboard-persistence";
import { PortfolioWithMetrics } from "@/utils/db/portfolio-queries";
import { formatDate, formatDistanceToNow } from "date-fns";
import { ro } from "date-fns/locale";
import {
  BarChart3,
  Calendar,
  Edit,
  MoreVertical,
  PieChart,
  Plus,
  Trash2,
  TrendingUp,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface PortfolioCardProps {
  portfolio: PortfolioWithMetrics;
  className?: string;
  onAddTransaction?: (portfolioId: string) => void;
}

export function PortfolioCard({
  portfolio,
  className,
  onAddTransaction,
}: PortfolioCardProps) {
  const { metrics } = portfolio;
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const router = useRouter();

  const topHoldings = metrics.holdings.slice(0, 3);

  const handleDeleteClick = () => {
    setShowDeleteDialog(true);
  };

  const handleDeleteSuccess = () => {
    setShowDeleteDialog(false);
  };

  const handleDashboardNavigation = () => {
    const preferences = {
      selectedPortfolios: [portfolio.id],
      displayCurrency: "EUR" as const,
      timestamp: new Date().toISOString(),
    };

    saveDashboardPreferences(preferences);

    router.push("/dashboard");
  };

  return (
    <Card
      className={cn(
        "flex flex-col h-full",
        "bg-gradient-to-br from-[#fafafa] via-[#e7e7e7] to-[#cfcfcf]",
        "dark:from-[#44454d] dark:via-[#242528] dark:to-[#1a1b1d]",
        "border border-black/10 dark:border-white/20",
        "shadow-md",
        "rounded-xl",
        "backdrop-blur-sm",
        className
      )}
    >
      <CardHeader className="flex-shrink-0 pb-4">
        <div className="flex items-start justify-between gap-4">
          <div className="space-y-1 min-h-[3.5rem] flex flex-col justify-start">
            <CardTitle className="text-xl font-semibold leading-tight">
              <Link
                href={`/portfolios/${portfolio.id}`}
                className="hover:underline"
                title="Vezi tranzacțiile pe portofoliu"
              >
                {portfolio.name}
              </Link>
            </CardTitle>
            {portfolio.description && (
              <CardDescription className="text-sm text-muted-foreground overflow-hidden text-ellipsis">
                {portfolio.description}
              </CardDescription>
            )}
          </div>
          <div className="flex items-center flex-shrink-0">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  aria-label="Acțiuni portofoliu"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem asChild>
                  <Link
                    href={`/portfolios/${portfolio.id}/edit`}
                    className="flex items-center gap-2"
                  >
                    <Edit className="h-4 w-4 dark:text-white" />
                    Editează portofoliul
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onAddTransaction?.(portfolio.id)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4 dark:text-white" />
                  Adaugă tranzacție
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleDeleteClick}
                  variant="destructive"
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Șterge portofoliul
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col px-6 pb-4">
        <div className="grid grid-cols-2 gap-4 mb-6 flex-shrink-0">
          <div
            onClick={handleDashboardNavigation}
            className="flex items-center gap-3 p-3 rounded-lg bg-muted/50 hover:cursor-pointer hover:bg-muted/90 transition-colors duration-200"
          >
            <div className="p-2 rounded-md bg-portavio-blue/10">
              <PieChart className="h-4 w-4 text-portavio-blue" />
            </div>
            <div title="Vezi pozițiile din dashboard">
              <p className="text-sm font-medium">{metrics.totalHoldings}</p>
              <p className="text-xs text-muted-foreground">Poziții active</p>
            </div>
          </div>

          <Link href={`/portfolios/${portfolio.id}`}>
            <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50 hover:cursor-pointer hover:bg-muted/90 transition-colors duration-200">
              <div className="p-2 rounded-md bg-portavio-orange/10">
                <BarChart3 className="h-4 w-4 text-portavio-orange" />
              </div>
              <div title="Vezi tranzacțiile">
                <p className="text-sm font-medium">
                  {metrics.totalTransactions}
                </p>
                <p className="text-xs text-muted-foreground">Tranzacții</p>
              </div>
            </div>
          </Link>
        </div>

        <div className="flex-1 flex flex-col">
          <div className="flex items-center gap-2 mb-3 flex-shrink-0">
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
            <h4 className="text-sm font-medium">Top poziții</h4>
            <Link
              href={`/portfolios/${portfolio.id}`}
              className="hover:underline text-xs text-muted-foreground italic"
            >
              (Vezi toate)
            </Link>
          </div>

          <div className="space-y-2 min-h-[9rem] flex flex-col">
            {topHoldings.length > 0 ? (
              topHoldings.map((holding) => (
                <div
                  key={holding.ticker}
                  className="flex items-center justify-between p-2 rounded-md bg-muted/80"
                >
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">
                      {holding.ticker}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {holding.totalQuantity.toLocaleString("ro-RO", {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 2,
                      })}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {holding.transactionCount} tranzacții
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="flex items-center justify-center h-full text-sm text-muted-foreground">
                Nu există poziții active
              </div>
            )}
          </div>
        </div>

        <div className="flex-shrink-0 mt-4 min-h-[1.5rem] flex items-center">
          {metrics.lastTransactionDate && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>
                Cea mai recentă tranzacție:{" "}
                {formatDate(metrics.lastTransactionDate, "PPPP", {
                  locale: ro,
                })}
              </span>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="flex-shrink-0 mt-auto pt-0 px-6 flex justify-between">
        <div className="text-xs text-muted-foreground">
          Creat{" "}
          {formatDistanceToNow(portfolio.created_at, {
            addSuffix: true,
            locale: ro,
          })}
        </div>
      </CardFooter>

      <DeletePortfolioDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        portfolio={portfolio}
        onSuccess={handleDeleteSuccess}
      />
    </Card>
  );
}
