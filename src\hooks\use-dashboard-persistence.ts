"use client";

import { useState, useEffect, useCallback } from "react";
import type { SupportedCurrency } from "@/components/dashboard/currency-selector";
import {
  getDashboardPreferences,
  saveDashboardPreferences,
  clearDashboardPreferences,
  getDefaultDashboardPreferences,
  type DashboardPreferences,
} from "@/lib/dashboard-persistence";

interface UseDashboardPersistenceOptions {
  initialDisplayCurrency?: SupportedCurrency;
}

export function useDashboardPersistence({
  initialDisplayCurrency = "EUR",
}: UseDashboardPersistenceOptions = {}) {
  const [selectedPortfolios, setSelectedPortfolios] = useState<string[]>([]);
  const [displayCurrency, setDisplayCurrency] = useState<SupportedCurrency>(
    initialDisplayCurrency
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load preferences from localStorage on mount
  useEffect(() => {
    try {
      const stored = getDashboardPreferences();

      if (stored) {
        // Use stored preferences as the single source of truth
        setSelectedPortfolios(stored.selectedPortfolios || []);
        setDisplayCurrency(stored.displayCurrency || initialDisplayCurrency);
      } else {
        // No stored preferences, use defaults
        const defaults = getDefaultDashboardPreferences();
        setSelectedPortfolios(defaults.selectedPortfolios);
        setDisplayCurrency(initialDisplayCurrency);
      }
    } catch (error) {
      console.error("Error loading dashboard preferences:", error);
      // Fallback to defaults on error
      const defaults = getDefaultDashboardPreferences();
      setSelectedPortfolios(defaults.selectedPortfolios);
      setDisplayCurrency(initialDisplayCurrency);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [initialDisplayCurrency]);

  // Save preferences to localStorage whenever they change (but only after initialization)
  useEffect(() => {
    if (!isInitialized) return;

    const preferences: DashboardPreferences = {
      selectedPortfolios,
      displayCurrency,
      timestamp: new Date().toISOString(),
    };

    saveDashboardPreferences(preferences);
  }, [selectedPortfolios, displayCurrency, isInitialized]);

  // Wrapped setters that maintain the same API as regular useState
  const updateSelectedPortfolios = useCallback((portfolios: string[]) => {
    setSelectedPortfolios(portfolios);
  }, []);

  const updateDisplayCurrency = useCallback((currency: SupportedCurrency) => {
    setDisplayCurrency(currency);
  }, []);

  // Utility function to clear all preferences
  const clearPreferences = useCallback(() => {
    clearDashboardPreferences();
    const defaults = getDefaultDashboardPreferences();
    setSelectedPortfolios(defaults.selectedPortfolios);
    setDisplayCurrency(defaults.displayCurrency);
  }, []);

  // Utility function to reset to default values
  const resetToDefaults = useCallback(() => {
    const defaults = getDefaultDashboardPreferences();
    setSelectedPortfolios(defaults.selectedPortfolios);
    setDisplayCurrency(initialDisplayCurrency);
  }, [initialDisplayCurrency]);

  return {
    // Current state
    selectedPortfolios,
    displayCurrency,

    // State setters
    setSelectedPortfolios: updateSelectedPortfolios,
    setDisplayCurrency: updateDisplayCurrency,

    // Loading state
    isLoading,
    isInitialized,

    // Utility functions
    clearPreferences,
    resetToDefaults,

    // Check if preferences are persisted
    hasPersistedPreferences: getDashboardPreferences() !== null,
  };
}
