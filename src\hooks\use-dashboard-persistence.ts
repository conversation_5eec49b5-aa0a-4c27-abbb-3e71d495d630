"use client";

import { useState, useEffect, useCallback } from "react";
import type { SupportedCurrency } from "@/components/dashboard/currency-selector";
import {
  getDashboardPreferences,
  saveDashboardPreferences,
  clearDashboardPreferences,
  getDefaultDashboardPreferences,
  type DashboardPreferences,
} from "@/lib/dashboard-persistence";

interface UseDashboardPersistenceOptions {
  initialSelectedPortfolios?: string[];
  initialDisplayCurrency?: SupportedCurrency;
}

export function useDashboardPersistence({
  initialSelectedPortfolios = [],
  initialDisplayCurrency = "EUR",
}: UseDashboardPersistenceOptions = {}) {
  const [selectedPortfolios, setSelectedPortfolios] = useState<string[]>(
    initialSelectedPortfolios
  );
  const [displayCurrency, setDisplayCurrency] = useState<SupportedCurrency>(
    initialDisplayCurrency
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load preferences from localStorage on mount
  useEffect(() => {
    try {
      const stored = getDashboardPreferences();
      
      if (stored) {
        // If we have URL parameters (initial values), they take precedence over stored preferences
        // This maintains backward compatibility with URL-based portfolio selection
        const portfoliosToUse = initialSelectedPortfolios.length > 0 
          ? initialSelectedPortfolios 
          : stored.selectedPortfolios || [];
        
        const currencyToUse = stored.displayCurrency || initialDisplayCurrency;

        setSelectedPortfolios(portfoliosToUse);
        setDisplayCurrency(currencyToUse);
      } else {
        // No stored preferences, use initial values or defaults
        setSelectedPortfolios(initialSelectedPortfolios);
        setDisplayCurrency(initialDisplayCurrency);
      }
    } catch (error) {
      console.error("Error loading dashboard preferences:", error);
      // Fallback to initial values on error
      setSelectedPortfolios(initialSelectedPortfolios);
      setDisplayCurrency(initialDisplayCurrency);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [initialSelectedPortfolios, initialDisplayCurrency]);

  // Save preferences to localStorage whenever they change (but only after initialization)
  useEffect(() => {
    if (!isInitialized) return;

    const preferences: DashboardPreferences = {
      selectedPortfolios,
      displayCurrency,
      timestamp: new Date().toISOString(),
    };

    saveDashboardPreferences(preferences);
  }, [selectedPortfolios, displayCurrency, isInitialized]);

  // Wrapped setters that maintain the same API as regular useState
  const updateSelectedPortfolios = useCallback((portfolios: string[]) => {
    setSelectedPortfolios(portfolios);
  }, []);

  const updateDisplayCurrency = useCallback((currency: SupportedCurrency) => {
    setDisplayCurrency(currency);
  }, []);

  // Utility function to clear all preferences
  const clearPreferences = useCallback(() => {
    clearDashboardPreferences();
    const defaults = getDefaultDashboardPreferences();
    setSelectedPortfolios(defaults.selectedPortfolios);
    setDisplayCurrency(defaults.displayCurrency);
  }, []);

  // Utility function to reset to initial values
  const resetToInitial = useCallback(() => {
    setSelectedPortfolios(initialSelectedPortfolios);
    setDisplayCurrency(initialDisplayCurrency);
  }, [initialSelectedPortfolios, initialDisplayCurrency]);

  return {
    // Current state
    selectedPortfolios,
    displayCurrency,
    
    // State setters
    setSelectedPortfolios: updateSelectedPortfolios,
    setDisplayCurrency: updateDisplayCurrency,
    
    // Loading state
    isLoading,
    isInitialized,
    
    // Utility functions
    clearPreferences,
    resetToInitial,
    
    // Check if preferences are persisted
    hasPersistedPreferences: getDashboardPreferences() !== null,
  };
}
