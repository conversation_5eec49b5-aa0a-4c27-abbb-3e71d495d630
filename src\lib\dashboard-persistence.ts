import type { SupportedCurrency } from "@/components/dashboard/currency-selector";

const STORAGE_KEY = "portavio-dashboard-preferences";

export interface DashboardPreferences {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
  timestamp: string;
}

interface DashboardPreferencesData {
  preferences: DashboardPreferences;
  version: string; // For future migration compatibility
}

const CURRENT_VERSION = "1.0.0";

/**
 * Get the current dashboard preferences from localStorage
 * This is a utility function for server-side or non-hook usage
 */
export function getDashboardPreferences(): Partial<DashboardPreferences> | null {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const data: DashboardPreferencesData = JSON.parse(stored);
      return data.preferences;
    }
  } catch (error) {
    console.error("Error reading dashboard preferences from localStorage:", error);
  }

  return null;
}

/**
 * Save dashboard preferences to localStorage
 */
export function saveDashboardPreferences(preferences: DashboardPreferences): void {
  if (typeof window === "undefined") {
    return;
  }

  const data: DashboardPreferencesData = {
    preferences: {
      ...preferences,
      timestamp: new Date().toISOString(),
    },
    version: CURRENT_VERSION,
  };

  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error("Error saving dashboard preferences to localStorage:", error);
  }
}

/**
 * Clear dashboard preferences from localStorage
 */
export function clearDashboardPreferences(): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error("Error clearing dashboard preferences from localStorage:", error);
  }
}

/**
 * Get default dashboard preferences
 */
export function getDefaultDashboardPreferences(): DashboardPreferences {
  return {
    selectedPortfolios: [],
    displayCurrency: "EUR",
    timestamp: new Date().toISOString(),
  };
}
